import { useStaticQuery, graphql } from 'gatsby';

export const useSocialLinks = () => {
    const allSocialLinks = useStaticQuery(
        graphql`
            query GET_SOCIAL_LINK {
                wp {
                    acfOptionsGlobalSettings {
                        globalSettings {
                            socialFacebookLink
                            socialInstagramLink
                            socialTwitterLink
                            socialYoutubeLink
                            socialPinterestLink
                            socialYelpLink
                            cartLink
                        }
                    }
                }
            }
        `
    );
    return allSocialLinks.wp.acfOptionsGlobalSettings.globalSettings;
};
