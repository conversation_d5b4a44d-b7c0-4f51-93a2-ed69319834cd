import { useStaticQuery, graphql } from 'gatsby';

export const useAllBlog = () => {
    const AllPosts = useStaticQuery(
        graphql`
            query GET_All_POSTS {
                allWordpressPost {
                    nodes {
                        featured_media {
                            sourceUrl
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(
                                        width: 600
                                        placeholder: DOMINANT_COLOR
                                        formats: [AUTO, WEBP]
                                        quality: 90
                                    )
                                }
                            }
                        }
                        categories {
                            name
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                        wordpress_id
                    }
                }
            }
        `
    );
    return AllPosts;
};
