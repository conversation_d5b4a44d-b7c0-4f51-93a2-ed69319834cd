import { useStaticQuery, graphql } from 'gatsby';

export const useAllBlog = () => {
    const AllPosts = useStaticQuery(
        graphql`
            query GET_All_POSTS {
                allWpPost {
                    nodes {
                        categories {
                            nodes {
                                name
                            }
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                        databaseId
                    }
                }
            }
        `
    );
    return AllPosts;
};
