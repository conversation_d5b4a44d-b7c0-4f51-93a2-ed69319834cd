import React from 'react';
import { graphql, Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import Footer from '../components/Footer';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import '../styles/app.scss';

const blog = ({ data }) => {
    const post = data.wpPage;
    let test_title = post.seo?.title || post.title;
    let new_seo_title = test_title.replace("&#039;", "'");
    const { allWpPost: articles } = data;
    const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];
    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.excerpt}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Read Our Blog" />

            <section className="page-section">
                <div className="container blogwrapper">
                    <div className="bloglftwrap">
                        {articles.nodes.map(article => {
                            const image = getImage(article.featuredImage?.node?.localFile);
                            return (
                            <div className="bloglft" key={article.id}>
                                <div className="blogimg">
                                    {image ? (
                                        <GatsbyImage
                                            image={image}
                                            alt={article.title}
                                        />
                                    ) : article.featuredImage?.node?.sourceUrl ? (
                                        <img
                                            src={article.featuredImage.node.sourceUrl}
                                            alt={article.title}
                                        />
                                    ) : null}
                                </div>
                                <div className="bloxexc">
                                    <Link
                                        to={`/${article.slug}`}
                                        className="postName"
                                    >
                                        <h2
                                            dangerouslySetInnerHTML={{
                                                __html: article.title,
                                            }}
                                        />
                                    </Link>
                                    <h5>
                                        {
                                            months[
                                                new Date(
                                                    article.date
                                                ).getMonth()
                                            ]
                                        }{' '}
                                        {new Date(article.date).getDate()},
                                        {new Date(article.date).getFullYear()}
                                    </h5>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: article.excerpt,
                                        }}
                                    />

                                    <Link to={`/${article.slug}`}>
                                        Read More
                                    </Link>
                                </div>
                                <img
                                    className="blogline"
                                    src={LineFull}
                                    alt="linefull"
                                />
                            </div>
                            );
                        })}
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};
export default blog;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            seo {
                metaDesc
                title
                opengraphDescription
            }
        }
        allWpPost (sort: { date: DESC }){
            nodes {
                id
                slug
                categories {
                    nodes {
                        name
                        slug
                    }
                }
                excerpt
                title
                date
            }
        }
    }
`;
