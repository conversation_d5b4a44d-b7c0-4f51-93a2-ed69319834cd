{"name": "gatsby-starter-default", "private": true, "description": "A simple starter to get up and developing quickly with Gatsby", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"@glidejs/glide": "^3.4.1", "axios": "^0.19.2", "dotenv": "^8.2.0", "gatsby": "^2.19.32", "gatsby-awesome-pagination": "^0.3.5", "gatsby-image": "^2.2.43", "gatsby-plugin-facebook-pixel": "^1.0.3", "gatsby-plugin-google-analytics": "^2.3.0", "gatsby-plugin-load-script": "^1.0.6", "gatsby-plugin-manifest": "^2.2.44", "gatsby-plugin-netlify": "^2.1.34", "gatsby-plugin-offline": "^3.0.32", "gatsby-plugin-purgecss": "^4.0.1", "gatsby-plugin-react-helmet": "^3.1.21", "gatsby-plugin-sass": "^2.1.28", "gatsby-plugin-sharp": "^2.4.3", "gatsby-source-filesystem": "^2.1.46", "gatsby-source-wordpress": "^3.1.62", "gatsby-transformer-sharp": "^2.3.17", "jquery": "^3.4.1", "lodash": "^4.17.15", "nanoid": "^2.1.11", "node-sass": "^4.13.1", "oauth-signature": "^1.5.0", "prop-types": "^15.7.2", "react": "^16.12.0", "react-dom": "^16.12.0", "react-helmet": "^5.2.1", "react-load-script": "0.0.6", "react-magnific-popup": "^1.0.1", "react-slick": "^0.25.2", "reactjs-popup": "^1.5.0", "scroll-into-view": "^1.14.2", "slick-carousel": "^1.8.1"}, "devDependencies": {"babel-eslint": "^9.0.0", "eslint": "^5.16.0", "eslint-config-airbnb": "^17.1.1", "eslint-config-prettier": "^4.3.0", "eslint-config-wesbos": "0.0.19", "eslint-plugin-html": "^5.0.5", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.18.3", "eslint-plugin-react-hooks": "^1.7.0", "prettier": "^1.19.1"}, "keywords": ["gatsby"], "license": "MIT", "scripts": {"build": "gatsby build", "develop": "gatsby develop", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "start": "npm run develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}}