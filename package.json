{"name": "gatsby-starter-default", "private": true, "description": "A simple starter to get up and developing quickly with Gatsby", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"@glidejs/glide": "^3.4.1", "ajv": "^8.0.0", "axios": "^0.27.2", "dotenv": "^16.0.3", "gatsby": "^4.25.9", "gatsby-awesome-pagination": "^0.3.6", "gatsby-image": "^3.11.0", "gatsby-plugin-facebook-pixel": "^1.0.8", "gatsby-plugin-google-analytics": "^3.14.0", "gatsby-plugin-image": "^2.25.0", "gatsby-plugin-load-script": "^1.1.0", "gatsby-plugin-manifest": "^4.25.0", "gatsby-plugin-netlify": "^5.1.0", "gatsby-plugin-offline": "^5.25.0", "gatsby-plugin-purgecss": "^6.1.1", "gatsby-plugin-react-helmet": "^5.25.0", "gatsby-plugin-sass": "^5.25.0", "gatsby-plugin-sharp": "^4.25.1", "gatsby-source-filesystem": "^4.25.0", "gatsby-source-wordpress": "^7.15.0", "gatsby-transformer-sharp": "^4.25.0", "jquery": "^3.4.1", "lodash": "^4.17.15", "nanoid": "^2.1.11", "oauth-signature": "^1.5.0", "prop-types": "^15.7.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-load-script": "0.0.6", "react-magnific-popup": "^1.0.1", "react-slick": "^0.25.2", "reactjs-popup": "^1.5.0", "sass": "^1.77.0", "scroll-into-view": "^1.14.2", "sharp": "^0.34.2", "slick-carousel": "^1.8.1"}, "devDependencies": {"@babel/eslint-parser": "^7.17.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.5.0", "eslint-config-wesbos": "^2.0.1", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "prettier": "^2.6.2"}, "keywords": ["gatsby"], "license": "MIT", "scripts": {"build": "gatsby build", "develop": "gatsby develop", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "start": "npm run develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}}