import { useStaticQuery, graphql } from 'gatsby';

export const useTestimonials = () => {
    const HomeTestimonials = useStaticQuery(
        graphql`
            query GET_HOME_TESTIMONIALS_CONTENT {
                wpPage(databaseId: { eq: 5 }) {
                    testimonials {
                        whatParentsSayList {
                            parentsTestimonial
                            parentsName
                        }
                        whatParentsSayImage {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(
                                        width: 600
                                        placeholder: DOMINANT_COLOR
                                        formats: [AUTO, WEBP]
                                        quality: 90
                                    )
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return HomeTestimonials;
};
