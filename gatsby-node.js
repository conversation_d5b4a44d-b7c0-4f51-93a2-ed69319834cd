/* eslint-disable prefer-const */
const _ = require('lodash');
const path = require('path');

// const { createFilePath } = require('gatsby-source-filesystem');
// const { paginate } = require('gatsby-awesome-pagination');

const getOnlyPublished = edges =>
    _.filter(edges, ({ node }) => node.status === 'publish');

const monthArchive = {
    2030: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2029: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2028: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2027: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2026: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2025: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2024: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	2023: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2022: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	2021: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	
    2020: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },

    2019: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2018: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2017: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2016: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2015: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2014: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2013: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2012: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2011: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
};

const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
];
exports.createPages = ({ actions, graphql }) => {
    const { createPage } = actions;
    return graphql(`
        {
            allWordpressPage {
                edges {
                    node {
                        id
                        title
                        slug
                        wordpress_id
                        template
                        status
                    }
                }
            }
        }
    `)
        .then(result => {
            if (result.errors) {
                // result.errors.forEach(e => console.error(e.toString()));
                return Promise.reject(result.errors);
            }
            const allPages = result.data.allWordpressPage.edges;
            let pageTemplate = path.resolve(`./src/templates/page.js`);

            const pages =
                process.env.NODE_ENV === 'production'
                    ? getOnlyPublished(allPages)
                    : allPages;

            // Call `createPage()` once per WordPress page
            _.each(pages, ({ node: page }) => {
                switch (page.template) {
                    case 'template_about.php':
                        pageTemplate = path.resolve(`./src/templates/about.js`);
                        break;
                    case 'template_openplay.php':
                        pageTemplate = path.resolve(
                            `./src/templates/openplay.js`
                        );
                        break;
                    case 'template_whywerock.php':
                        pageTemplate = path.resolve(
                            `./src/templates/whywerock.js`
                        );
                        break;
                    case 'template_playwithapurpose.php':
                        pageTemplate = path.resolve(
                            `./src/templates/playwithapurpose.js`
                        );
                        break;
                    case 'template_reviews.php':
                        pageTemplate = path.resolve(
                            `./src/templates/reviews.js`
                        );
                        break;
                    case 'template_nonprofit.php':
                        pageTemplate = path.resolve(
                            `./src/templates/nonprofit.js`
                        );
                        break;
                    case 'template_werockcare.php':
                        pageTemplate = path.resolve(
                            `./src/templates/werockcare.js`
                        );
                        break;
                    case 'template_birthdayparties.php':
                        pageTemplate = path.resolve(
                            `./src/templates/birthdayparties.js`
                        );
                        break;
                    case 'template_werockonwheels.php':
                        pageTemplate = path.resolve(
                            `./src/templates/werockonwheels.js`
                        );
                        break;
                    case 'template_locations.php':
                        pageTemplate = path.resolve(
                            `./src/templates/locations.js`
                        );
                        break;
                    case 'template_specialevents.php':
                        pageTemplate = path.resolve(
                            `./src/templates/specialevents.js`
                        );
                        break;
                    case 'template_faq.php':
                        pageTemplate = path.resolve(`./src/templates/faq.js`);
                        break;
                    case 'template_sncalendar.php':
                        pageTemplate = path.resolve(
                            `./src/templates/calendar.js`
                        );
                        break;
                    case 'template_resources.php':
                        pageTemplate = path.resolve(
                            `./src/templates/resources.js`
                        );
                        break;
                    case 'template_werecommend.php':
                        pageTemplate = path.resolve(
                            `./src/templates/werecommend.js`
                        );
                        break;
                    case 'template_schools_out.php':
                        pageTemplate = path.resolve(
                            `./src/templates/schools-out.js`
                        );
                        break;
                    case 'template_schedule.php':
                        pageTemplate = path.resolve(
                            `./src/templates/schedule.js`
                        );
                        break;
                    case 'template_blog.php':
                        pageTemplate = path.resolve(`./src/templates/blog.js`);
                        break;
                    case 'template_contact.php':
                        pageTemplate = path.resolve(
                            `./src/templates/contact.js`
                        );
                        break;
					case 'template_giftcards.php':
						pageTemplate = path.resolve(
							`./src/templates/giftcards.js`
						);
						break;
                    case 'template_events.php':
                        pageTemplate = path.resolve(
                            `./src/templates/events.js`
                        );
                        break;
                    default:
                        pageTemplate = path.resolve(`./src/templates/page.js`);
                }

                createPage({
                    path: `/${page.slug}/`,
                    component: pageTemplate,
                    context: {
                        id: page.id,
                    },
                });
            });
        })

        .then(() =>
            graphql(`
                {
                    allWordpressPost(sort: { order: DESC, fields: date }) {
                        edges {
                            node {
                                id
                                date
                                slug
                                date
                                status
                                wordpress_id
                            }
                        }
                    }
                }
            `)
        )
        .then(result => {
            if (result.errors) {
                result.errors.forEach(e => console.error(e.toString()));
                return Promise.reject(result.errors);
            }
            const blogTemplate = path.resolve(`./src/templates/blog-single.js`);
            const archiveTemplate = path.resolve(`./src/templates/archive.js`);

            const blogs = result.data.allWordpressPost.edges;

            // Iterate over the array of clients
            _.each(blogs, ({ node: blog }) => {
                // Create the Gatsby page for this WordPress post
                createPage({
                    path: `/${blog.slug}/`,
                    component: blogTemplate,
                    context: {
                        id: blog.id,
                    },
                });
                // populate post archive object with real data
                const postMonth = new Date(blog.date).getMonth();
                const postYear = new Date(blog.date).getFullYear();
                const postId = blog.wordpress_id;
                monthArchive[postYear][postMonth].push([postId]);
            });

            // console.log(monthArchive);
            // go through monthArchive and create pages  for each month
            Object.keys(monthArchive).forEach(year => {
                const yearName = year;
                Object.keys(monthArchive[year]).forEach(month => {
                    const monthName = monthNames[month];
                    const postsIn = monthArchive[year][month];
                    const postNumber = monthArchive[year][month].length;
                    let postsList = [];

                    if (postNumber > 0) {
                        Object.keys(monthArchive[year][month]).forEach(post => {
                            Object.entries(
                                monthArchive[year][month][post]
                            ).forEach(([key, value]) => {
                                postsList = postsList.concat(value);
                            });
                        });
                        let postsListString = postsList.toString();
                        createPage({
                            path: `/archive/${monthName}-${yearName}/`,
                            component: archiveTemplate,
                            context: {
                                postIdString: postsListString,
                                postId: postsList,
                                title: `Archive: ${monthName} ${yearName}`,
                            },
                        });
                    }
                });
            });
        })
        .then(() =>
            graphql(`
                {
                    allWordpressWpEventsPostType {
                        edges {
                            node {
                                id
                                slug
                                status
                            }
                        }
                    }
                }
            `)
        )
        .then(result => {
            if (result.errors) {
                result.errors.forEach(e => console.error(e.toString()));
                return Promise.reject(result.errors);
            }
            const eventTemplate = path.resolve(
                `./src/templates/single-event.js`
            );

            const events = result.data.allWordpressWpEventsPostType.edges;

            // Iterate over the array of events
            _.each(events, ({ node: event }) => {
                // Create the Gatsby page for this WordPress post
                createPage({
                    path: `/${event.slug}/`,
                    component: eventTemplate,
                    context: {
                        id: event.id,
                    },
                });
            });
        })
        .then(() =>
            graphql(`
                {
                    allWordpressWpResourcesPostType {
                        edges {
                            node {
                                id
                                slug
                                status
                            }
                        }
                    }
                }
            `)
        )
        .then(result => {
            if (result.errors) {
                result.errors.forEach(e => console.error(e.toString()));
                return Promise.reject(result.errors);
            }
            const resourceTemplate = path.resolve(
                `./src/templates/singleResource.js`
            );

            const resources = result.data.allWordpressWpResourcesPostType.edges;

            // Iterate over the array of resources
            _.each(resources, ({ node: resource }) => {
                // Create the Gatsby page for this WordPress post
                createPage({
                    path: `/resource/${resource.slug}/`,
                    component: resourceTemplate,
                    context: {
                        id: resource.id,
                    },
                });
            });
        })
        .then(() =>
            graphql(`
                {
                    allWordpressCategory(filter: { count: { gt: 0 } }) {
                        edges {
                            node {
                                id
                                name
                                slug
                            }
                        }
                    }
                }
            `)
        )
        .then(result => {
            if (result.errors) {
                result.errors.forEach(e => console.error(e.toString()));
                return Promise.reject(result.errors);
            }

            const categoriesTemplate = path.resolve(
                `./src/templates/category.js`
            );

            // Create a Gatsby page for each WordPress Category
            _.each(result.data.allWordpressCategory.edges, ({ node: cat }) => {
                createPage({
                    path: `/category/${cat.slug}/`,
                    component: categoriesTemplate,
                    context: {
                        name: cat.name,
                        slug: cat.slug,
                        id: cat.id,
                    },
                });
            });
        });
};
