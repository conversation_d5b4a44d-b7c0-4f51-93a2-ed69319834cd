import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import PurposeList from '../components/pages/PurposesList';
import PosterPurposeDownload from '../components/pages/PosterPurposeDownload';
import SEO from '../components/seo';
import '../styles/app.scss';

const PlayWithPurpose = ({ data }) => {
    const post = data.wpPage;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");
    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.seo?.opengraphDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <PurposeList purposeList={post.playWithPurpose} />
            <PosterPurposeDownload posterData={post.playWithPurpose} />
            <Footer />
        </>
    );
};

export default PlayWithPurpose;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            playWithPurpose {
                playWithAPurposeDescription
                purposeList {
                    purposeText
                    purposeName
                    purposeStartImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                    purposeKidImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
                purposePosterDescription
                purposePosterTitle
                purposePosterImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                width: 600
                                placeholder: DOMINANT_COLOR
                                formats: [AUTO, WEBP]
                                quality: 90
                            )
                        }
                    }
                }
            }
            seo {
                metaDesc
                title
                opengraphDescription
            }
        }
    }
`;
