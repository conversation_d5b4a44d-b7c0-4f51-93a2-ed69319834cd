import { useStaticQuery, graphql } from 'gatsby';

export const useAppContent = () => {
    const AppContent = useStaticQuery(
        graphql`
            query GET_HOMEPAGE_APP_CONTENT {
                wpPage(databaseId: { eq: 5 }) {
                    appSection {
                        appContent
                        appTitle
                        appRightSideImage {
                            sourceUrl
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(
                                        width: 600
                                        placeholder: DOMINANT_COLOR
                                        formats: [AUTO, WEBP]
                                        quality: 90
                                    )
                                }
                            }
                        }
                        appNonProfitLink
                        appLearnMoreLink
                        appDownloadText
                    }
                }
            }
        `
    );
    return AppContent;
};
