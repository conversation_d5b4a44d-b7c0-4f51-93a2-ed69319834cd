import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import SEO from '../components/seo';
import HeroSingle from '../components/pages/HeroSingle';
import WhyWe from '../components/pages/WhyWe';
import SpecializedEquipmentList from '../components/pages/SpecializedEquipmentList';
import PosterDownload from '../components/pages/PosterDownload';
import LineFull from '../images/linefull.jpg';
import '../styles/app.scss';

const WhyWeRock = ({ data }) => {
    const post = data.wpPage;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.yoast_meta}
                description={
                    post.yoast_json_ld[0].wordpress__graph[0].description
                }
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <WhyWe content={post.content} featImage={post.featured_media} />
            <SpecializedEquipmentList
                eqList={post.acf.specialized_pieces_list}
                eqTitle={post.acf.equipment_title}
            />
            <section>
                <div className="container">
                    <img
                        className="margin-0-auto"
                        src={LineFull}
                        alt="line full"
                    />
                </div>
            </section>
            <PosterDownload
                posterImage={post.acf.wwr_poster_image}
                posterContent={post.acf.wwr_poster_download_content}
            />
            <Footer />
        </>
    );
};

export default WhyWeRock;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                width: 800
                                placeholder: DOMINANT_COLOR
                                formats: [AUTO, WEBP]
                                quality: 90
                            )
                        }
                    }
                }
            }
            seo {
                metaDesc
                title
                opengraphDescription
            }
            content
            whyWeRock {
                equipmentTitle
                specializedPiecesList {
                    equipmentName
                    equipmentImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                    equipmentDescription
                    equipmentEncourages
                    equipmentStarImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
                wwrPosterDownloadContent
                wwrPosterImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                width: 600
                                placeholder: DOMINANT_COLOR
                                formats: [AUTO, WEBP]
                                quality: 90
                            )
                        }
                    }
                }
            }
        }
    }
`;
