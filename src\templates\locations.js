import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LocationsList from '../components/pages/LocationsList';
import LocationsPage from '../components/pages/LocationTabsF';
import SEO from '../components/seo';
import '../styles/app.scss';

const Locations = ({ data }) => {
    const post = data.wpPage;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.seo?.opengraphDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section
                className="page-section bg-secondary text-white"
                id="locgreen"
            >
                <div className="container smallestwdt">
                      {/* <h2 className="yellowtxt">
                        <span>122 Destinations</span>
                        <span className="commaloc">,</span>{' '}
                        <span>27 States</span>
                        <span className="commaloc">,</span>{' '}
                        <span>8 Countries</span>
                    </h2> */}
                    <h2 className="yellowtxt locnumbers"
                                dangerouslySetInnerHTML={{
                                    __html: post.locations?.locationsTitle,
                                }}
                          />
                    <h4>
                        We strive to create the best sensory gym for autism and
                        provide the best indoor playground franchise opportunity
                        available. Here at WRTS, we want your gym to be THE KIDS
                        GYM in your city.
                        <br />
                        Let’s grow your play franchise together.
                    </h4>
                </div>
            </section>
        <LocationsList
                locationsUsa={post.locations?.listLocationsUsa}
                locationsUsaCs={post.locations?.listLocationsComingSoonToUsa}
                locationsInt={post.locations?.listLocationsInternational}
                locationsIntCs={
                    post.locations?.listLocationsComingSoonInternational
                }
            />
            {/* <LocationsPage /> */}
           
            <Footer />
        </>
    );
};

export default Locations;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            locations {
                locationsTitle
                listLocationsUsa {
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    contactUsUrl
                    locationImageUsa {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
                listLocationsInternational {
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    contactUsUrl
                    locationImageInt {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
                listLocationsComingSoonToUsa {
                    contactUsUrl
                    locationInfo
                    locationName
                    viewOnMapUrl
                    viewPhotosUrl
                    visitWebsiteUrl
                    locationImageCUsa {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
                listLocationsComingSoonInternational {
                    contactUsUrl
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    locationImageCInt {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 600
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                }
            }
            seo {
                metaDesc
                title
                opengraphDescription
            }
        }
    }
`;
