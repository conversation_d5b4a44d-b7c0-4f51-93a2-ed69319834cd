import React from 'react';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import { useStaffMembers } from '../../hooks/getStaffMembers';

const Staff = bgType => {
    const data = useStaffMembers();
    const StaffContent = data.wp?.acfOptionsGlobalSettings?.globalSettings;
    const StaffTitle = StaffContent?.ourStaffSectionTitle;
    const StaffList = StaffContent?.ourStaffListMembers || [];

    let bgClass = bgType.bgType;
    if (bgType.bgType === 'secondary') {
        bgClass = 'bg-secondary';
    } else {
        bgClass = 'bg-primary';
    }

    return (
        <section className={`page-section ${bgClass} text-white centersec staffsec display_none`}>
            <div className="container">
                <h2
                    className="staffttl"
                    dangerouslySetInnerHTML={{
                        __html: StaffTitle,
                    }}
                />
                {StaffList.map(staffMember => {
                    const image = getImage(staffMember.staffMemberImage?.localFile);
                    return (
                    <div
                        className="staffmember"
                        key={staffMember.staffMemberFullname}
                    >
                        {image && (
                            <GatsbyImage
                                className="whiteborderimg"
                                image={image}
                                alt={staffMember.staffMemberFullname}
                            />
                        )}
                        <h3
                            dangerouslySetInnerHTML={{
                                __html: staffMember.staffMemberFullname,
                            }}
                        />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: staffMember.staffMemberPosition,
                            }}
                        />
                    </div>
                    );
                })}
            </div>
        </section>
    );
};

export default Staff;
