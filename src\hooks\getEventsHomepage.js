import { useStaticQuery, graphql } from 'gatsby';

export const useEventsHome = () => {
    const eventsHome = useStaticQuery(
        graphql`
            query GET_EVENTS_HOME {
                allWpEvent(limit: 3) {
                    nodes {
                        slug
                        title
                        id
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 400
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                        }
                        eventDetails {
                            eventDate
                        }
                    }
                }
            }
        `
    );
    return eventsHome.allWpEvent.nodes;
};
