import { useStaticQuery, graphql } from 'gatsby';

export const useResourcesTeam = () => {
    const ResourcesTeam = useStaticQuery(
        graphql`
            query GET_RESOURCE_TEAM {
                allWpPost(
                    filter: {
                        categories: {
                            nodes: { elemMatch: { slug: { eq: "resources-team" } } }
                        }
                    }
                ) {
                    nodes {
                        excerpt
                        slug
                        id
                        title
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 400
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return ResourcesTeam.allWpPost.nodes;
};
