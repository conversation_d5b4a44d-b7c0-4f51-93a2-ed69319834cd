import React from 'react';
import { graphql } from 'gatsby';
import SEO from '../components/seo';
import Footer from '../components/Footer';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import '../styles/app.scss';

const Page = ({ data }) => {
    const post = data.wpPage;

    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.seo?.opengraphDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section className="page-section smallestwdt respitesec">
                <div
                    className="container"
                    dangerouslySetInnerHTML={{
                        __html: post.content,
                    }}
                />
            </section>
            <Footer />
        </>
    );
};
export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            content
            slug
            seo {
                metaDesc
                title
                opengraphDescription
            }
        }
    }
`;

export default Page;
