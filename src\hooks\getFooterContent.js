import { useStaticQuery, graphql } from 'gatsby';

export const useFooterContent = () => {
    const footerContent = useStaticQuery(
        graphql`
            query GET_FOOTER_CONTENT {
                wp {
                    acfOptionsGlobalSettings {
                        globalSettings {
                            footerAddress
                            footerPhoneNumber
                        }
                    }
                }
            }
        `
    );
    return footerContent.wp.acfOptionsGlobalSettings.globalSettings;
};
