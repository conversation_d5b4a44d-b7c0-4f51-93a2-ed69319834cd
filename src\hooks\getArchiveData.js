import { useStaticQuery, graphql } from 'gatsby';

export const useArchiveData = () => {
    const ArchiveData = useStaticQuery(
        graphql`
            query GET_ARCHIVE_DATA {
                allWpPost(sort: { date: DESC }) {
                    nodes {
                        date
                        slug
                        title
                        id
                        databaseId
                    }
                }
            }
        `
    );
    return ArchiveData;
};
