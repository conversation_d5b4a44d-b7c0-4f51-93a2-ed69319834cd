import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import '../styles/app.scss';

const SingleEvent = ({ data }) => {
    const eventData = data.wpEvent;
    const test_title = eventData.seo?.title || eventData.title;
    const new_seo_title = test_title.replace('&#039;', "'");
    return (
        <>
            <SEO
                title={new_seo_title}
                meta={eventData.seo?.metaDesc}
                description={eventData.excerpt}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Event" />

            <section className="page-section">
                <div className="container blogwrapper blogarticle">
                    <div className="bloglft">
                        <div className="blogimg">
                            <img
                                src={
                                    eventData.featuredImage?.node?.localFile
                                        ?.childImageSharp?.gatsbyImageData?.images?.fallback?.src
                                }
                                alt={eventData.title}
                            />
                        </div>
                        <div className="bloxexc">
                            <h2
                                dangerouslySetInnerHTML={{
                                    __html: eventData.title,
                                }}
                            />
                            <h5
                                dangerouslySetInnerHTML={{
                                    __html: eventData.eventDetails?.eventDate,
                                }}
                            />
                        </div>
                        <img
                            className="blogline"
                            src={LineFull}
                            alt="lineful"
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: eventData.content,
                            }}
                        />
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export const pageQuery = graphql`
    query($id: String!) {
        wordpressWpEventsPostType(id: { eq: $id }) {
            featured_media {
                localFile {
                    childImageSharp {
                        fluid {
                            srcWebp
                            srcSetWebp
                            srcSet
                            src
                        }
                    }
                }
            }
            acf {
                event_date
            }
            content
            title
            yoast_meta {
                name
                property
            }
            yoast_title
        }
    }
`;

export default SingleEvent;
