import React from 'react';
import { graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import Helmet from 'react-helmet';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import '../styles/app.scss';
import SEO from '../components/seo';

const WeRockCare = ({ data }) => {
    const { wpPage: post } = data;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    // Get the image for gatsby-plugin-image (disabled for now)
    // const image = getImage(post.weRockCare.weRockCareImage?.localFile);

    console.log(post.seo?.metaDesc || 'No meta description');

    return (
        <>
            <SEO
                title={new_seo_title}
                description={post.seo?.metaDesc || post.seo?.opengraphDescription || ''}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt respitesec padding-less-under">
                <div className="container">
                    <div className="openplft">
                        {post.weRockCare?.weRockCareImage?.sourceUrl ? (
                            <img
                                src={post.weRockCare.weRockCareImage.sourceUrl}
                                alt="respite"
                            />
                        ) : null}

                    </div>

                    <div className="openprgt withmarbtm">
                        <h2 className="bluetxt">WHY WE ROCK CARE?</h2>
                        <p
                            className="lastitem"
                            dangerouslySetInnerHTML={{
                                __html: post.weRockCare?.weRockCareContent || '',
                            }}
                        />
                    </div>
				 <div className="werockcareheal">
					 {/* <h5 class="bluetxt">Book we rock care</h5> */}
                  {/* <div
                            className=""
                            dangerouslySetInnerHTML={{
                                __html: post.acf.book_we_rock_care_link,
                            }}
                        />   */}
						</div>   
                    <div className="werockcareheal">
                        <h5 class="bluetxt">Book we rock care</h5>
                        <iframe
                            src="https://wordpress-1462486-5644809.cloudwaysapps.com/healcode-werock.php"
                            title="appointment"
                            className="healcode_widgets"
                        />
                    </div> 
                </div>
            </section>

            <section
                className="page-section bg-secondary text-white openphours"
                id="pricingsec"
            >
                <div className="container smallestwdt flexwrap">
                    <div className="twothirdcol flexbox toup">
                        <h2 className="yellowtxt">Pricing</h2>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.weRockCare?.whyWeRockCarePricing || '',
                            }}
                        />
                        <h5 className="yellowtxt addinfo">
                            ADDITIONAL INFORMATION
                        </h5>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.weRockCare?.whyWeRockCareAdditionalInformations || '',
                            }}
                        />
                    </div>

                    <div className="onethirdcol flexbox todwn">
                        {post.weRockCare?.whyWeRockCareFormsList?.map(
                            (formItem, i) => (
                                <a
                                    href={formItem.pdfFormFile?.sourceUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="wrtsbtn yellowbtn fullbtn"
                                    key={i}
                                    dangerouslySetInnerHTML={{
                                        __html: formItem.pdfFormName,
                                    }}
                                />
                            )
                        )}
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default WeRockCare;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            seo {
                metaDesc
                title
                opengraphDescription
            }
            weRockCare {
                weRockCareTitle
                weRockCareContent
                weRockCareImage {
                    sourceUrl
                    localFile {
                        url
                    }
                }
                bookWeRockCareLink
                whyWeRockCarePricing
                whyWeRockCareAdditionalInformations
                whyWeRockCareFormsList {
                    pdfFormName
                    pdfFormFile {
                        sourceUrl
                    }
                }
            }
        }
    }
`;
