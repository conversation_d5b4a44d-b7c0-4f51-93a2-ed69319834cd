import { useStaticQuery, graphql } from 'gatsby';

export const useMenuitems = () => {
    const AllMenuItems = useStaticQuery(
        graphql`
            query GET_ALL_MENU_ITEMS {
                wpMenu(slug: { eq: "main-menu" }) {
                    menuItems {
                        nodes {
                            label
                            url
                            databaseId
                            childItems {
                                nodes {
                                    label
                                    url
                                    databaseId
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return AllMenuItems.wpMenu.menuItems.nodes;
};
