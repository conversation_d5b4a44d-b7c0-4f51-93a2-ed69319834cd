import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import WhatParentsSay from '../components/pages/WhatParentsSay';
import YelpReviews from '../components/pages/YelpReviews';
import SEO from '../components/seo';
import '../styles/app.scss';

const Reviews = ({ data }) => {
    const post = data.wpPage;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.seo?.opengraphDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <WhatParentsSay />
            <Footer />
        </>
    );
};

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            seo {
                metaDesc
                title
                opengraphDescription
            }
            reviews {
                yelpReviews {
                    yelpReviewCode
                }
            }
        }
    }
`;

export default Reviews;
