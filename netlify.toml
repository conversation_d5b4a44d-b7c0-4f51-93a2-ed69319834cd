[build]
  publish = "public"
  command = "npm run build:prod"

[build.environment]
  NODE_VERSION = "20"
  NPM_VERSION = "10"
  GATSBY_CPU_COUNT = "4"
  GATSBY_CONCURRENT_DOWNLOAD = "5"

# Production context
[context.production]
  command = "npm run build:prod"

# Deploy preview context
[context.deploy-preview]
  command = "npm run build"

# Branch deploy context
[context.branch-deploy]
  command = "npm run build"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Cache static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.webp"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Cache CSS and JS
[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Redirects for SEO
[[redirects]]
  from = "/home"
  to = "/"
  status = 301

# WordPress admin redirects (if needed)
[[redirects]]
  from = "/wp-admin/*"
  to = "https://wordpress-1462486-5644809.cloudwaysapps.com/wp-admin/:splat"
  status = 302

# 404 fallback
[[redirects]]
  from = "/*"
  to = "/404/"
  status = 404
