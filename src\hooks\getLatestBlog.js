import { useStaticQuery, graphql } from 'gatsby';

export const useLatestBlog = () => {
    const LatestPosts = useStaticQuery(
        graphql`
            query GET_LATEST_POSTS {
                allWpPost(limit: 5) {
                    nodes {
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 400
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                        }
                        categories {
                            nodes {
                                name
                            }
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                    }
                }
            }
        `
    );
    return LatestPosts;
};
