/* eslint-disable jsx-a11y/anchor-has-content */
import React from 'react';
import { Link } from 'gatsby';
import FooterFr from '../images/fr-footer.png';
import { useFooterMenuitems } from '../hooks/getFooterMenuItems';
import { useSocialLinks } from '../hooks/getSocialLinks';
import { useFooterContent } from '../hooks/getFooterContent';

function MenuItemLink(data) {
    if (data.url.startsWith('http')) {
        return (
            <a href={data.url} target="_blank" rel="noopener noreferrer">
                {data.title}
            </a>
        );
    }
    const postTypeUrl = data.url
        .split('https://wordpress-1462486-5644809.cloudwaysapps.com/')
        .pop();
    return <Link to={`/${postTypeUrl}`}>{data.title}</Link>;
}

const Footer = () => {
    const menuItems = useFooterMenuitems();
    const socialLinks = useSocialLinks();
    const footerContent = useFooterContent();
    return (
        <>
            <footer>
                <div className="footaddress">
                    <h2>
                        {footerContent.footerAddress}
                        {` `}
                        <span className="ipadhide">|</span>
                        {` `}
                        <span className="yellowtxt">
                            CALL US: {footerContent.footerPhoneNumber}
                        </span>
                    </h2>
                </div>

                <h3 className="footinst">
                    FOLLOW US ON INSTAGRAM {` `}
                    <a
                        className="yellowtxt"
                        href={socialLinks.socialInstagramLink}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        @werockalpharetta
                    </a>
                </h3>

                <div className="footer-nav">
                    {menuItems.map(menuItem => (
                        <MenuItemLink
                            url={menuItem.url}
                            title={menuItem.label}
                            key={menuItem.databaseId}
                        />
                    ))}
                </div>
                <div className="sign-box-up">
                <p className="sign-box-up-par"><span>JOIN OUR NEWSLETTER!</span><br />Subscribe to get the latest updates on events, classes, and more!</p>
                    <a
                        className="signup-letter-new"
                        href="https://axle.ws/KfFxJ/subscribe/"
                        target="_blank"
                        rel="noopener noreferrer"
                    >SUBSCRIBE</a>
                    </div>   
                <div className="headersocial footsoc soc">
                    <a
                        className="soc-facebook"
                        href={socialLinks.socialFacebookLink}
                        target="_blank"
                        rel="noopener noreferrer"
                    ></a>
                    <a
                        className="soc-instagram"
                        href={socialLinks.socialInstagramLink}
                        target="_blank"
                        rel="noopener noreferrer"
                    ></a>
      {/*               <a className='tiktok'
                        href="https://www.tiktok.com/@infoqnqz81?_t=8ksr1bhzvea&_r=1"
                        target="_blank"
                        rel="noopener noreferrer">
                        <img src="https://wordpress-1462486-5644809.cloudwaysapps.com/wp-content/uploads/2024/02/TikTok-Icon-1.svg" alt='tiktok' />
                    </a>
            <a
                        className="soc-pinterest"
                        href={socialLinks.social_pinterest_link}
                        target="_blank"
                        rel="noopener noreferrer"
                    ></a> 
            <a
                        className="soc-twitter"
                        href={socialLinks.social_twitter_link}
                        target="_blank"
                        rel="noopener noreferrer"
                    ></a>  */}
                    {/* <a
                        className="soc-yelp soc-icon-last"
                        href={socialLinks.social_yelp_link}
                        target="_blank"
                        rel="noopener noreferrer"
                    ></a> */}
                </div>
               
                <a
                    href="http://www.franchiseregistry.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <img className="footerfr" src={ 'https://werockthespectrumkidsgym.wrtsfranchise.com/wp-content/uploads/2022/07/FRANdata_FranchiseRegistryMember_Logo_2022.png' } alt="footer fr" />
                </a>
            </footer>
        </>
    );
};
export default Footer;
