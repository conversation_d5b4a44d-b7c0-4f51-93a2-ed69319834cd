import { useStaticQuery, graphql } from 'gatsby';

export const useStaffMembers = () => {
    const AllStaffMembers = useStaticQuery(
        graphql`
            query GET_ALL_STAFF_MEMBERS {
                wp {
                    acfOptionsGlobalSettings {
                        globalSettings {
                            ourStaffListMembers {
                                staffMemberFullname
                                staffMemberPosition
                                staffMemberImage {
                                    localFile {
                                        childImageSharp {
                                            gatsbyImageData(
                                                width: 300
                                                placeholder: DOMINANT_COLOR
                                                formats: [AUTO, WEBP]
                                                quality: 90
                                            )
                                        }
                                    }
                                }
                            }
                            ourStaffSectionTitle
                        }
                    }
                }
            }
        `
    );
    return AllStaffMembers;
};
