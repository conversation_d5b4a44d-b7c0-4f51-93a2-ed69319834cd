import { useStaticQuery, graphql } from 'gatsby';

export const useHeroContent = () => {
    const HeroContent = useStaticQuery(
        graphql`
            query GET_HOMEPAGE_HERO_CONTENT {
                wpPage(databaseId: { eq: 5 }) {
                    heroSection {
                        heroSubtitle
                        heroButtonText
                        heroButtonLink
                        heroWelcomeTitle
                        heroVideoFileWebp {
                            sourceUrl
                        }
                        heroVideoFileMp4 {
                            sourceUrl
                        }
                    }
                }
            }
        `
    );
    return HeroContent;
};
