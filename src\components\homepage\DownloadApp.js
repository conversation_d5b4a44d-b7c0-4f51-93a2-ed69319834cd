import React from 'react';
import { Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import Line from '../../images/line.jpg';
import AppStore from '../../images/wrtsapp.jpg';
import GooglePlay from '../../images/wrtsgoogle.jpg';
import { useAppContent } from '../../hooks/getAppContent';

const DownloadApp = () => {
    const data = useAppContent();
    const appData = data.wpPage?.appSection;
    return (
        <section className="page-section smallwdt" id="abouthome">
            <div className="container">
                <div className="homeglobelft alignmidd">
                    <h2
                        className="bluetxt"
                        dangerouslySetInnerHTML={{
                            __html: appData?.appTitle,
                        }}
                    />
                    <p
                        dangerouslySetInnerHTML={{
                            __html: appData?.appContent,
                        }}
                    />

                    <Link
                        className="wrtsbtn yellowbtn"
                        to={appData?.appLearnMoreLink}
                    >
                        LEARN MORE
                    </Link>
                    <Link
                        className="wrtsbtn bluebtn"
                        to={appData?.appNonProfitLink}
                    >
                        NON PROFIT
                    </Link>
                    <img className="line" src={Line} alt="line" />
                    <div className="abouthomeapp">
                        <h6
                            className="bluetxt"
                            dangerouslySetInnerHTML={{
                                __html: appData?.appDownloadText,
                            }}
                        />
                        <a
                            href="https://apps.apple.com/us/app/we-rock-the-spectrum-2-0/id1555275663"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <img src={AppStore} alt="AppStore Icon" />
                        </a>
                        {` `}
                        <a
                            href="https://play.google.com/store/apps/details?id=com.werockthespectrum"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <img src={GooglePlay} alt="GooglePlay icon" />
                        </a>
                    </div>
                </div>
                <div className="homeglobergt alignmidd">
                    {appData?.appRightSideImage?.localFile && (
                        <GatsbyImage
                            image={getImage(appData.appRightSideImage.localFile)}
                            className="globe"
                            alt="globe"
                        />
                    )}
                </div>
            </div>
        </section>
    );
};
export default DownloadApp;
