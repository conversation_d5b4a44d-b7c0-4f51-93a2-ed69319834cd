import { useStaticQuery, graphql } from 'gatsby';

export const useHeaderInfo = () => {
	const headerInfo = useStaticQuery(
		graphql`
            query GET_HEADER_INFO {
                wp {
                    acfOptionsGlobalSettings {
                        globalSettings {
                            logoHeader {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 200
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                            headerWorkingHours
                            headerRegisterButtonText
                            headerContactNumber
                            headerBannerMessageContent
                            enableHeaderBanner
                        }
                    }
                }
            }
        `
	);
	return headerInfo.wp.acfOptionsGlobalSettings.globalSettings;
};
