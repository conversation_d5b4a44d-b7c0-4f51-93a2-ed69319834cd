import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import Footer from '../components/Footer';
import SEO from '../components/seo';
import '../styles/app.scss';

const WeRecommend = ({ data }) => {
    const post = data.wpPage;
    const test_title = post.seo?.title || post.title;
    const new_seo_title = test_title.replace('&#039;', "'");

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.yoast_meta}
                description={
                    post.yoast_json_ld[0].wordpress__graph[0].description
                }
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section className="page-section centersec smallestwdt nopaddbottsec">
                <div className="container">
                    <h2
                        className="bluetxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.we_recommend_heading,
                        }}
                    />
                    <p
                        className="lastitem biggertxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.we_recommend_description,
                        }}
                    />

                    <img
                        className="schoolsline"
                        src={LineFull}
                        alt="linefull"
                    />
                </div>

                <div className="werecommwrap">
                    {post.weRecommend?.weRecommendLogos?.map((logo, i) => (
                        <div className="recommimg" key={i}>
                            <a
                                href={logo.wecommendUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <img
                                    src={
                                        logo.recommendLogo?.localFile
                                            ?.childImageSharp?.gatsbyImageData?.images?.fallback?.src
                                    }
                                    alt="werec"
                                />
                            </a>
                        </div>
                    )) || []}
                </div>
            </section>
            <Footer />
        </>
    );
};

export default WeRecommend;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            seo {
                metaDesc
                title
                opengraphDescription
            }
            weRecommend {
                weRecommendHeading
                weRecommendDescription
                weRecommendLogos {
                    recommendLogo {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    width: 400
                                    placeholder: DOMINANT_COLOR
                                    formats: [AUTO, WEBP]
                                    quality: 90
                                )
                            }
                        }
                    }
                    wecommendUrl
                }
            }
        }
    }
`;
