import React from 'react';
import { Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import { useServicesHome } from '../../hooks/getServicesHome';

const Services = () => {
    const servicesData = useServicesHome();
    const services = servicesData.wpPage?.services?.serviceBoxesList || [];
    return (
        <section className="homethreeboxes page-section bg-primary text-white">
            <div className="container flexwrap">
                {services.map(service => {
                    const image = getImage(service.serviceImage?.localFile);
                    return (
                    <div className="hbox flexbox" key={service.serviceTitle}>
                        {image && (
                            <GatsbyImage
                                image={image}
                                className="whiteborderimg"
                                alt={service.serviceTitle}
                            />
                        )}
                        <h3
                            dangerouslySetInnerHTML={{
                                __html: service.serviceTitle,
                            }}
                        />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: service.serviceDescription,
                            }}
                        />
                        <Link
                            to={service.serviceLink}
                            className="wrtsbtn yellowbtn"
                        >
                            MORE INFO
                        </Link>
                    </div>
                    );
                })}
            </div>
        </section>
    );
};
export default Services;
