import { useStaticQuery, graphql } from 'gatsby';

export const useFooterMenuitems = () => {
    const AllFooterMenuItems = useStaticQuery(
        graphql`
            query GET_FOOTER_MENU_ITEMS {
                wpMenu(slug: { eq: "footer-menu" }) {
                    menuItems {
                        nodes {
                            label
                            url
                            databaseId
                            childItems {
                                nodes {
                                    label
                                    url
                                    databaseId
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return AllFooterMenuItems.wpMenu.menuItems.nodes;
};
