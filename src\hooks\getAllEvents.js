import { useStaticQuery, graphql } from 'gatsby';

export const useAllEvents = () => {
    const allEvents = useStaticQuery(
        graphql`
            query GET_ALL_EVENTS {
                allWpEvent {
                    nodes {
                        slug
                        title
                        id
                        excerpt
                        date
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 600
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                        }
                        eventDetails {
                            eventDate
                        }
                    }
                }
            }
        `
    );
    return allEvents.allWpEvent.nodes;
};
