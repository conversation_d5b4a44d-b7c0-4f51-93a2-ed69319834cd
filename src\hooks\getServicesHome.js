import { useStaticQuery, graphql } from 'gatsby';

export const useServicesHome = () => {
    const servicesHome = useStaticQuery(
        graphql`
            query GET_SERVICES_HOME {
                wpPage(databaseId: { eq: 5 }) {
                    services {
                        serviceBoxesList {
                            serviceDescription
                            serviceTitle
                            serviceLink
                            serviceImage {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            width: 400
                                            placeholder: DOMINANT_COLOR
                                            formats: [AUTO, WEBP]
                                            quality: 90
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        `
    );
    return servicesHome;
};
