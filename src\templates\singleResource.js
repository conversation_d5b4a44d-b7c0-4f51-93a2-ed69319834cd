import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import SEO from '../components/seo';
import '../styles/app.scss';

const singleResources = ({ data }) => {
    const post = data.wpResourcesPostType;
    return (
        <>
            <SEO
                title={post.seo?.title || post.title}
                meta={post.seo?.metaDesc}
                description={post.excerpt}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Resources" />
            <section className="page-section">
                <div className="container blogwrapper blogarticle">
                    <div className="bloglft">
                        <div className="blogimg">
                            {post.featuredImage?.node && (
                                <img
                                    src={post.featuredImage.node.localFile?.childImageSharp?.gatsbyImageData?.images?.fallback?.src}
                                    alt={post.title}
                                />
                            )}
                        </div>
                        <div className="bloxexc">
                            <h2
                                dangerouslySetInnerHTML={{
                                    __html: post.title,
                                }}
                            />
                        </div>
                        <img
                            className="blogline"
                            src={LineFull}
                            alt="lineful"
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.content,
                            }}
                        />
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};
export default singleResources;

export const pageQuery = graphql`
    query($id: String!) {
        wpResource(id: { eq: $id }) {
            id
            title
            slug
            content
            seo {
                metaDesc
                title
                opengraphDescription
            }
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                width: 800
                                placeholder: DOMINANT_COLOR
                                formats: [AUTO, WEBP]
                                quality: 90
                            )
                        }
                    }
                }
            }
        }
    }
`;
