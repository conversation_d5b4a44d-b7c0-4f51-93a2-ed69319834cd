import React from 'react';
import PropTypes from 'prop-types';

export default function HTML(props) {
    return (
        <html {...props.htmlAttributes}>
            <head>
                <meta charSet="utf-8" />
                <meta httpEquiv="x-ua-compatible" content="ie=edge" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1, shrink-to-fit=no"
                />
                <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>
                <script src="https://widgets.mindbodyonline.com/javascripts/healcode.js" type="text/javascript"></script>
                {props.headComponents}
                <script
  async
                    src="https://www.googletagmanager.com/gtag/js?id=G-9BQ024HYV4"
/>

<script
  dangerouslySetInnerHTML={{
    __html: `
    window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-9BQ024HYV4');
  `,
  }}
/>
<script
            dangerouslySetInnerHTML={{
                __html: `
                (function(){ var s = document.createElement('script'); var h = document.querySelector('head') || document.body; s.src = 'https://acsbapp.com/apps/app/dist/js/app.js'; s.async = true; s.onload = function(){ acsbJS.init({ statementLink : '', footerHtml : 'Web Accessibility by WRTS', hideMobile : false, hideTrigger : false, disableBgProcess : false, language : 'en', position : 'right', leadColor : '#006FB9 ', triggerColor : '#006FB9 ', triggerRadius : '50%', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerIcon : 'people', triggerSize : 'medium', triggerOffsetX : 20, triggerOffsetY : 20, mobile : { triggerSize : 'small', triggerPositionX : 'right', triggerPositionY : 'center', triggerOffsetX : 10, triggerOffsetY : 0, triggerRadius : '50%' } }); }; h.appendChild(s); })();
            `,
            }}
            />
            </head>
            <body {...props.bodyAttributes}>
                {props.preBodyComponents}
                <div
                    key="body"
                    id="___gatsby"
                    dangerouslySetInnerHTML={{ __html: props.body }}
                />
                {props.postBodyComponents}
                <script
                  dangerouslySetInnerHTML={{
                    __html: `
                    var element = document.getElementById("mainNav");
                    window.addEventListener("scroll", function () {
                        if (document.documentElement.scrollTop > 400) {
                        element.classList.add("fixedd");
                        }else{
                        element.classList.remove("fixedd");
                      }
                    }, false);
                    $("body").on("click", "[data-popup]", function(){
                        var xx = $(this).data("popup");
                        console.log(xx);
                        $("." + xx).addClass("show");
                    });
                    $("body").on('click', ".close_popup", function(){
                        $(".popup-wrap").removeClass('show');
                    });
                    `
                  }}
                />        
            </body>
        </html>
    );
}

HTML.propTypes = {
    htmlAttributes: PropTypes.object,
    headComponents: PropTypes.array,
    bodyAttributes: PropTypes.object,
    preBodyComponents: PropTypes.array,
    body: PropTypes.string,
    postBodyComponents: PropTypes.array,
};
