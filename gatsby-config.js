// Set environment variables for better performance
process.env.GATSBY_CONCURRENT_DOWNLOAD = process.env.GATSBY_CONCURRENT_DOWNLOAD || '1';

require('dotenv').config({
    path: `.env.${process.env.NODE_ENV}`,
});

module.exports = {
    siteMetadata: {
        title: `We Rock The Spectrum - Kids Gym & Sensory Playground`,
        description: `We Rock The Spectrum provides a sensory-safe environment for children with autism and special needs through our specialized gym equipment and programs. Find locations nationwide.`,
        author: `We Rock The Spectrum`,
        siteUrl: process.env.GATSBY_SITE_URL || `https://werockthespectrum.com`,
        keywords: `autism, sensory gym, kids gym, special needs, sensory playground, occupational therapy, physical therapy, birthday parties`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        // Temporarily disable image processing to avoid corrupt image errors
        // `gatsby-plugin-image`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaults: {
                    formats: [`auto`, `webp`, `avif`],
                    placeholder: `dominantColor`,
                    quality: 90,
                    breakpoints: [750, 1080, 1366, 1920],
                    backgroundColor: `transparent`,
                },
                failOn: 'none', // Don't fail build on image processing errors
                stripMetadata: true, // Remove EXIF data for smaller files
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `We Rock The Spectrum - Kids Gym & Sensory Playground`,
                short_name: `WRTS`,
                description: `We Rock The Spectrum provides a sensory-safe environment for children with autism and special needs through our specialized gym equipment and programs.`,
                start_url: `/`,
                background_color: `#ffffff`,
                theme_color: `#0066cc`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // Update this to actual WRTS logo
                icons: [
                    {
                        src: `src/images/fav.png`,
                        sizes: `192x192`,
                        type: `image/png`,
                    },
                    {
                        src: `src/images/fav.png`,
                        sizes: `512x512`,
                        type: `image/png`,
                    },
                ],
                cache_busting_mode: 'none',
            },
        },
        'gatsby-plugin-sass',
        {
            resolve: 'gatsby-plugin-sitemap',
            options: {
                excludes: ['/dev-404-page/', '/404/', '/404.html', '/offline-plugin-app-shell-fallback/'],
                query: `
                    {
                        allSitePage {
                            nodes {
                                path
                            }
                        }
                        allWpPost {
                            nodes {
                                slug
                                modifiedGmt
                            }
                        }
                        allWpPage {
                            nodes {
                                slug
                                modifiedGmt
                            }
                        }
                    }
                `,
                resolveSiteUrl: () => process.env.GATSBY_SITE_URL || 'https://werockthespectrum.com',
                serialize: ({ allSitePage, allWpPost, allWpPage }) => {
                    const pages = allSitePage.nodes.map(node => ({
                        url: node.path,
                        changefreq: 'weekly',
                        priority: 0.7,
                    }));

                    const posts = allWpPost.nodes.map(node => ({
                        url: `/${node.slug}/`,
                        changefreq: 'monthly',
                        priority: 0.8,
                        lastmod: node.modifiedGmt,
                    }));

                    const wpPages = allWpPage.nodes.map(node => ({
                        url: `/${node.slug}/`,
                        changefreq: 'monthly',
                        priority: 0.9,
                        lastmod: node.modifiedGmt,
                    }));

                    return [...pages, ...posts, ...wpPages];
                },
            },
        },
        {
            resolve: 'gatsby-plugin-robots-txt',
            options: {
                host: process.env.GATSBY_SITE_URL || 'https://werockthespectrum.com',
                sitemap: `${process.env.GATSBY_SITE_URL || 'https://werockthespectrum.com'}/sitemap-index.xml`,
                policy: [
                    {
                        userAgent: '*',
                        allow: '/',
                        disallow: ['/admin/', '/wp-admin/', '/wp-login.php'],
                    },
                ],
            },
        },
        {
            resolve: 'gatsby-source-wordpress',
            options: {
                url: process.env.GATSBY_WORDPRESS_URL || 'https://wordpress-1462486-5644809.cloudwaysapps.com/graphql',
                verbose: process.env.NODE_ENV === 'development', // Enable verbose logging only in development
                schema: {
                    timeout: process.env.NODE_ENV === 'production' ? 60000 : 30000,
                    requestConcurrency: parseInt(process.env.GATSBY_CONCURRENT_DOWNLOAD) || 5,
                    perPage: 20, // Optimize pagination
                },
                develop: {
                    hardCacheMediaFiles: true, // Cache media files during development
                    nodeUpdateInterval: 5000, // Check for updates every 5 seconds in development
                },
                production: {
                    hardCacheMediaFiles: true, // Cache media files in production
                },
                type: {
                    MediaItem: {
                        localFile: {
                            requestConcurrency: 2, // Limit concurrent image downloads
                            maxFileSizeBytes: 15000000, // 15MB max file size
                        },
                    },
                },
                html: {
                    useGatsbyImage: true, // Enable automatic Gatsby Image optimization
                    createStaticFiles: true, // Create static files for better performance
                    imageMaxWidth: 1920, // Max width for images
                    fallbackImageMaxWidth: 800, // Fallback image width
                },
                excludeFieldNames: ['blocksJSON', 'saveContent'], // Exclude unnecessary fields
            },
        },
              
        {
            // Removes unused css rules
            resolve: 'gatsby-plugin-purgecss',
            options: {
                // Only activate purging in production
                develop: false,
                // Purge only the main css file
                purgeOnly: ['src/styles/app.scss'],
                // Whitelist important classes
                whitelist: ['body', 'html', 'img', 'a', 'g-image'],
                whitelistPatterns: [/^slick/, /^carousel/, /^modal/, /^btn/, /^nav/, /^dropdown/],
                // Ignore files
                ignore: ['/node_modules/', '/src/styles/vendor/'],
                // Print removed selectors
                printRejected: false,
            },
        }, // must be after other CSS plugins
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                precachePages: [`/`, `/about/`, `/blog/`, `/locations/`],
                workboxConfig: {
                    skipWaiting: true,
                    clientsClaim: true,
                    runtimeCaching: [
                        {
                            // Cache WordPress images
                            urlPattern: /^https:\/\/wordpress-1462486-5644809\.cloudwaysapps\.com\/.*\.(png|jpg|jpeg|webp|svg|gif)$/,
                            handler: `CacheFirst`,
                            options: {
                                cacheName: `wordpress-images`,
                                expiration: {
                                    maxEntries: 200,
                                    maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
                                },
                            },
                        },
                        {
                            // Cache external resources
                            urlPattern: /^https:\/\/fonts\.googleapis\.com/,
                            handler: `StaleWhileRevalidate`,
                            options: {
                                cacheName: `google-fonts-stylesheets`,
                            },
                        },
                        {
                            // Cache other external requests
                            urlPattern: /^https?.*/,
                            handler: `NetworkFirst`,
                            options: {
                                cacheName: `external-resources`,
                                networkTimeoutSeconds: 3,
                                expiration: {
                                    maxEntries: 50,
                                    maxAgeSeconds: 24 * 60 * 60, // 24 hours
                                },
                            },
                        },
                    ],
                },
            },
        },
        'gatsby-plugin-netlify',
    ],
};
