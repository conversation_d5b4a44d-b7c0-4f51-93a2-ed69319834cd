module.exports = {
    siteMetadata: {
        title: `Wrts Migration sandbox`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaultQuality: 90,
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Migration sandbox`,
                short_name: `Wrts Migration sandbox`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // This path is relative to the root of the site.
            },
        },
        'gatsby-plugin-sass',
        {
            resolve: 'gatsby-source-wordpress',
            options: {
                // The base url to your WP site.
                baseUrl: 'https://wordpress-1462486-5644809.cloudwaysapps.com/',
                // WP.com sites set to true, WP.org set to false
                hostingWPCOM: false,
                // The protocol. This can be http or https.
                protocol: 'https',
                // Use 'Advanced Custom Fields' Wordpress plugin
                useACF: true,
                auth: {},
                // Set to true to debug endpoints on 'gatsby build'
                verboseOutput: false,
                includedRoutes: [
                    '**/pages',
                    '**/posts',
                    '**/categories',
                    '**/events_post_type',
                    '**/resources_post_type',
                    '**/media',
                    '**/options/**',
                    '**/wp-api-menus/v2/**',
                ],
                excludedRoutes: [
                    '**/wp/v2/users/me/**',
                    '**/wp/v2/settings/**',
                ],
            },
        },
              
        {
            // Removes unused css rules
            resolve: 'gatsby-plugin-purgecss',
            options: {
                // Activates purging in gatsby develop
                develop: true,
                // Purge only the main css file
                purgeOnly: ['style/app.scss'],
            },
        }, // must be after other CSS plugins
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                workboxConfig: {
                    skipWaiting: true,
                    clientsClaim: true,
                    runtimeCaching: [
                        {
                            urlPattern: /^https?.*/,
                            handler: `NetworkFirst`,
                        },
                    ],
                },
            },
        },
        'gatsby-plugin-netlify',
    ],
};
