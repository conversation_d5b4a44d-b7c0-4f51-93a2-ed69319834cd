module.exports = {
    siteMetadata: {
        title: `Wrts Migration sandbox`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        `gatsby-plugin-image`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaultQuality: 90,
                failOn: 'none',
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Migration sandbox`,
                short_name: `Wrts Migration sandbox`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // This path is relative to the root of the site.
            },
        },
        'gatsby-plugin-sass',
        {
            resolve: 'gatsby-source-wordpress',
            options: {
                url: 'https://wordpress-1462486-5644809.cloudwaysapps.com/graphql',
                verbose: false,
                develop: {
                    hardCacheMediaFiles: true,
                },
                production: {
                    hardCacheMediaFiles: false,
                },
                debug: {
                    graphql: {
                        writeQueriesToDisk: false,
                    },
                },
                type: {
                    MediaItem: {
                        localFile: {
                            requestConcurrency: 50,
                        },
                    },
                },
            },
        },
              
        {
            // Removes unused css rules
            resolve: 'gatsby-plugin-purgecss',
            options: {
                // Activates purging in gatsby develop
                develop: true,
                // Purge only the main css file
                purgeOnly: ['style/app.scss'],
            },
        }, // must be after other CSS plugins
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                workboxConfig: {
                    skipWaiting: true,
                    clientsClaim: true,
                    runtimeCaching: [
                        {
                            urlPattern: /^https?.*/,
                            handler: `NetworkFirst`,
                        },
                    ],
                },
            },
        },
        'gatsby-plugin-netlify',
    ],
};
