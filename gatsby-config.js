// Set environment variables for better performance
process.env.GATSBY_CONCURRENT_DOWNLOAD = process.env.GATSBY_CONCURRENT_DOWNLOAD || '1';

module.exports = {
    siteMetadata: {
        title: `Wrts Migration sandbox`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        // Temporarily disable image processing to avoid corrupt image errors
        // `gatsby-plugin-image`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaults: {
                    formats: [`auto`, `webp`, `avif`],
                    placeholder: `dominantColor`,
                    quality: 90,
                    breakpoints: [750, 1080, 1366, 1920],
                    backgroundColor: `transparent`,
                },
                failOn: 'none', // Don't fail build on image processing errors
                stripMetadata: true, // Remove EXIF data for smaller files
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Migration sandbox`,
                short_name: `Wrts Migration sandbox`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`,
            },
        },
        'gatsby-plugin-sass',
        {
            resolve: 'gatsby-source-wordpress',
            options: {
                url: 'https://wordpress-1462486-5644809.cloudwaysapps.com/graphql',
                verbose: false, // Disable verbose logging for production
                schema: {
                    timeout: 60000, // Standard timeout for production
                    requestConcurrency: 5, // Optimize for production performance
                    perPage: 20, // Optimize pagination
                },
                develop: {
                    hardCacheMediaFiles: true, // Cache media files during development
                },
                production: {
                    hardCacheMediaFiles: true, // Cache media files in production
                },
                type: {
                    MediaItem: {
                        localFile: {
                            requestConcurrency: 2, // Limit concurrent image downloads
                        },
                    },
                },
                html: {
                    useGatsbyImage: true, // Enable automatic Gatsby Image optimization
                    createStaticFiles: true, // Create static files for better performance
                },
            },
        },
              
        {
            // Removes unused css rules
            resolve: 'gatsby-plugin-purgecss',
            options: {
                // Activates purging in gatsby develop
                develop: true,
                // Purge only the main css file
                purgeOnly: ['style/app.scss'],
            },
        }, // must be after other CSS plugins
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                precachePages: [`/`, `/about/`, `/blog/`, `/locations/`],
                workboxConfig: {
                    skipWaiting: true,
                    clientsClaim: true,
                    runtimeCaching: [
                        {
                            // Cache WordPress images
                            urlPattern: /^https:\/\/wordpress-1462486-5644809\.cloudwaysapps\.com\/.*\.(png|jpg|jpeg|webp|svg|gif)$/,
                            handler: `CacheFirst`,
                            options: {
                                cacheName: `wordpress-images`,
                                expiration: {
                                    maxEntries: 200,
                                    maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
                                },
                            },
                        },
                        {
                            // Cache external resources
                            urlPattern: /^https:\/\/fonts\.googleapis\.com/,
                            handler: `StaleWhileRevalidate`,
                            options: {
                                cacheName: `google-fonts-stylesheets`,
                            },
                        },
                        {
                            // Cache other external requests
                            urlPattern: /^https?.*/,
                            handler: `NetworkFirst`,
                            options: {
                                cacheName: `external-resources`,
                                networkTimeoutSeconds: 3,
                                expiration: {
                                    maxEntries: 50,
                                    maxAgeSeconds: 24 * 60 * 60, // 24 hours
                                },
                            },
                        },
                    ],
                },
            },
        },
        'gatsby-plugin-netlify',
    ],
};
